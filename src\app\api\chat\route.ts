import { createPerplexity } from '@ai-sdk/perplexity';
import { streamText, stepCountIs } from 'ai';
import { SYSTEM_PROMPT } from './prompt';
import { getContact } from './tools/getContact';
import { getCrazy } from './tools/getCrazy';
import { getInternship } from './tools/getInternship';
import { getPresentation } from './tools/getPresentation';
import { getProjects } from './tools/getProjects';
import { getResume } from './tools/getResume';
import { getSkills } from './tools/getSkills';
import { getSports } from './tools/getSport';

export const maxDuration = 30;

// API Keys configuration with validation
const PRIMARY_API_KEY = process.env.PERPLEXITY_API_KEY;
const BACKUP_API_KEY = process.env.PERPLEXITY_API_KEY_BACKUP;

// Validate API keys on startup
if (!PRIMARY_API_KEY) {
  console.error('[CHAT-API] CRITICAL: PERPLEXITY_API_KEY environment variable is missing');
}

if (!BACKUP_API_KEY) {
  console.warn('[CHAT-API] WARNING: PERPLEXITY_API_KEY_BACKUP environment variable is missing - no fallback available');
}

// Helper function to create Perplexity model with specific API key
function createPerplexityModel(apiKey: string, modelName: string = 'sonar-pro') {
  try {
    console.log(`[CHAT-API] Creating Perplexity model with model: ${modelName}`);
    console.log(`[CHAT-API] API key format: ${apiKey.substring(0, 10)}...`);

    // Create the Perplexity provider using the correct AI SDK pattern
    const perplexityProvider = createPerplexity({
      apiKey: apiKey,
    });

    // Get the specific model from the provider
    const model = perplexityProvider(modelName);

    console.log(`[CHAT-API] Successfully created Perplexity model`);
    return model;

  } catch (error) {
    console.error('[CHAT-API] Error creating Perplexity model:', error);
    throw new Error(`Failed to create Perplexity model: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to validate and clean message structure
function validateAndCleanMessages(messages: any[]) {
  const cleanedMessages = [];
  let lastRole = null;

  for (const msg of messages) {
    const role = msg.role;
    const content = msg.parts?.map((part: any) => part.text).join('') || msg.content || '';

    // Skip empty messages
    if (!content.trim()) continue;

    // Ensure alternating pattern (skip consecutive messages from same role)
    if (role === lastRole && role !== 'system') {
      console.log(`[CHAT-API] Skipping consecutive ${role} message to maintain alternating pattern`);
      continue;
    }

    cleanedMessages.push({
      role: role,
      content: content,
    });

    lastRole = role;
  }

  return cleanedMessages;
}

// Enhanced error handler function with detailed logging
function errorHandler(error: unknown, context: string = 'Unknown') {
  console.error(`[CHAT-API] Error in ${context}:`, error);

  if (error == null) {
    return 'Unknown error occurred';
  }

  if (typeof error === 'string') {
    return error;
  }

  if (error instanceof Error) {
    // Log the full error details for debugging
    console.error(`[CHAT-API] Error details - Name: ${error.name}, Message: ${error.message}, Stack: ${error.stack}`);
    return error.message;
  }

  // For any other type of error, stringify it
  const stringified = JSON.stringify(error);
  console.error(`[CHAT-API] Stringified error: ${stringified}`);
  return stringified;
}

// Helper function to validate API key format
function validateApiKey(apiKey: string | undefined, keyName: string): boolean {
  if (!apiKey) {
    console.error(`[CHAT-API] ${keyName} is undefined or empty`);
    return false;
  }

  if (!apiKey.startsWith('pplx-')) {
    console.error(`[CHAT-API] ${keyName} does not have the expected 'pplx-' prefix`);
    return false;
  }

  if (apiKey.length < 20) {
    console.error(`[CHAT-API] ${keyName} appears to be too short`);
    return false;
  }

  console.log(`[CHAT-API] ${keyName} validation passed`);
  return true;
}

export async function POST(req: Request) {
  try {
    console.log('[CHAT-API] === Starting new chat request ===');

    // Validate API keys before processing
    const primaryKeyValid = validateApiKey(PRIMARY_API_KEY, 'PRIMARY_API_KEY');
    const backupKeyValid = validateApiKey(BACKUP_API_KEY, 'BACKUP_API_KEY');

    if (!primaryKeyValid && !backupKeyValid) {
      throw new Error('No valid API keys available. Please check your environment variables.');
    }

    const { messages } = await req.json();
    console.log('[CHAT-API] Incoming messages:', messages?.length || 0, 'messages');

    if (!messages || !Array.isArray(messages)) {
      throw new Error('Invalid messages format. Expected an array of messages.');
    }

    // Validate and clean messages to ensure proper alternating pattern
    const cleanedMessages = validateAndCleanMessages(messages);
    console.log('[CHAT-API] Cleaned messages count:', cleanedMessages.length);

    if (cleanedMessages.length === 0) {
      throw new Error('No valid messages to process after cleaning.');
    }

    // Format messages for the AI SDK
    const formattedMessages = [
      SYSTEM_PROMPT,
      ...cleanedMessages
    ];

    console.log('[CHAT-API] Total formatted messages:', formattedMessages.length);

    const tools = {
      getProjects,
      getPresentation,
      getResume,
      getContact,
      getSkills,
      getSports,
      getCrazy,
      getInternship,
    };

    // Try with primary API key first
    let result;
    let lastError;

    // Try with primary API key first
    if (primaryKeyValid && PRIMARY_API_KEY) {
      try {
        console.log('[CHAT-API] Attempting with primary API key...');
        const primaryModel = createPerplexityModel(PRIMARY_API_KEY);

        result = streamText({
          model: primaryModel,
          messages: formattedMessages,
          tools,
          stopWhen: stepCountIs(2),
        });

        console.log('[CHAT-API] Primary API key successful - returning stream response');
        return result.toUIMessageStreamResponse();

      } catch (primaryError) {
        console.error('[CHAT-API] Primary API key failed:', primaryError);
        lastError = primaryError;
      }
    } else {
      console.log('[CHAT-API] Skipping primary API key (invalid or missing)');
    }

    // Try with backup API key if primary failed or was invalid
    if (backupKeyValid && BACKUP_API_KEY) {
      try {
        console.log('[CHAT-API] Attempting with backup API key...');
        const backupModel = createPerplexityModel(BACKUP_API_KEY);

        result = streamText({
          model: backupModel,
          messages: formattedMessages,
          tools,
          stopWhen: stepCountIs(2),
        });

        console.log('[CHAT-API] Backup API key successful - returning stream response');
        return result.toUIMessageStreamResponse();

      } catch (backupError) {
        console.error('[CHAT-API] Backup API key also failed:', backupError);
        lastError = backupError;
      }
    } else {
      console.log('[CHAT-API] Skipping backup API key (invalid or missing)');
    }

    // If both keys failed, throw the last error
    console.error('[CHAT-API] All API keys failed. Last error:', lastError);
    throw lastError || new Error('All API keys failed and no error details available');

  } catch (err) {
    console.error('[CHAT-API] === Global error occurred ===');
    const errorMessage = errorHandler(err, 'POST request handler');

    // Create detailed error response
    const errorResponse = {
      error: errorMessage,
      timestamp: new Date().toISOString(),
      details: 'Chat API request failed',
      hasValidPrimaryKey: validateApiKey(PRIMARY_API_KEY, 'PRIMARY_API_KEY'),
      hasValidBackupKey: validateApiKey(BACKUP_API_KEY, 'BACKUP_API_KEY'),
      requestId: Math.random().toString(36).substring(7) // Simple request ID for tracking
    };

    console.error('[CHAT-API] Returning error response:', errorResponse);

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
  }
}

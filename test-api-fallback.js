// Test script to validate API key fallback mechanism
require('dotenv').config();
const { perplexity } = require('@ai-sdk/perplexity');
const { streamText } = require('ai');

// Test API keys
const PRIMARY_API_KEY = process.env.PERPLEXITY_API_KEY;
const BACKUP_API_KEY = process.env.PERPLEXITY_API_KEY_BACKUP;

console.log('🔍 Testing API Key Fallback Mechanism');
console.log('=====================================');

// Helper function to create Perplexity model with specific API key
function createPerplexityModel(apiKey, modelName = 'sonar-pro') {
  const client = perplexity({
    apiKey: apiKey,
  });
  return client(modelName);
}

// Test function
async function testAPIFallback() {
  const testMessages = [
    {
      role: 'system',
      content: 'You are a helpful assistant. Respond briefly.'
    },
    {
      role: 'user',
      content: 'Hello, can you respond with just "API test successful"?'
    }
  ];

  console.log('📝 Test messages prepared');
  console.log('🔑 Primary API Key:', PRIMARY_API_KEY ? `${PRIMARY_API_KEY.substring(0, 10)}...` : 'NOT SET');
  console.log('🔑 Backup API Key:', BACKUP_API_KEY ? `${BACKUP_API_KEY.substring(0, 10)}...` : 'NOT SET');
  console.log('');

  let result;
  let lastError;

  // Try primary API key
  try {
    console.log('🚀 Testing primary API key...');
    const primaryModel = createPerplexityModel(PRIMARY_API_KEY);
    
    result = await streamText({
      model: primaryModel,
      messages: testMessages,
      maxTokens: 50,
    });

    console.log('✅ Primary API key successful!');
    return { success: true, usedKey: 'primary', result };
    
  } catch (primaryError) {
    console.log('❌ Primary API key failed:', primaryError.message);
    lastError = primaryError;

    // Try backup API key
    if (BACKUP_API_KEY) {
      try {
        console.log('🔄 Testing backup API key...');
        const backupModel = createPerplexityModel(BACKUP_API_KEY);
        
        result = await streamText({
          model: backupModel,
          messages: testMessages,
          maxTokens: 50,
        });

        console.log('✅ Backup API key successful!');
        return { success: true, usedKey: 'backup', result };
        
      } catch (backupError) {
        console.log('❌ Backup API key also failed:', backupError.message);
        lastError = backupError;
      }
    } else {
      console.log('⚠️  No backup API key configured');
    }
  }

  return { success: false, error: lastError };
}

// Run the test
testAPIFallback()
  .then(async (result) => {
    if (result.success) {
      console.log('');
      console.log('🎉 FALLBACK MECHANISM WORKING!');
      console.log(`📊 Used: ${result.usedKey.toUpperCase()} API key`);
      
      // Try to get the response text
      try {
        const response = await result.result.text;
        console.log('📝 Response:', response);
      } catch (e) {
        console.log('📝 Response stream created successfully');
      }
    } else {
      console.log('');
      console.log('💥 BOTH API KEYS FAILED');
      console.log('Error:', result.error?.message || 'Unknown error');
    }
  })
  .catch((error) => {
    console.log('');
    console.log('💥 TEST SCRIPT ERROR');
    console.log('Error:', error.message);
  });
